import {
  Box,
  Flex,
  Text,
  Divider,
  useBreakpointValue,
  useMediaQuery,
} from '@chakra-ui/react';
import React from 'react';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import { useLayoutContext } from 'store/Layout';
import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { TextValor } from 'components/PDV/Text/TextValor';

interface TotalizadoresFixosProps {
  quantidadeItens: number;
  totalProdutos: number;
  valorTotalProdutos: number;
}

const TotalizadoresFixos: React.FC<TotalizadoresFixosProps> = ({
  quantidadeItens,
  totalProdutos,
  valorTotalProdutos,
}) => {
  const isResponsiveVersion = useBreakpointValue({
    base: true,
    md: false,
    lg: false,
  });
  const { casasDecimais } = usePadronizacaoContext();
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const { menuIsOpen } = useLayoutContext();
  return (
    <Box
      position={isLargerThan900 ? 'fixed' : 'relative'}
      bottom={isLargerThan900 ? '69px' : '0px'}
      bg="gray.50"
      w={`calc(100% - ${menuIsOpen ? '210px' : '108px'})`}
      boxShadow="0 -4px 12px rgba(0, 0, 0, 0.1)"
      zIndex={10}
      h={{ base: 'auto', md: '52px' }}
      minH="52px"
      py={{ base: '12px', md: '0' }}
      px={{ base: '16px', md: '24px' }}
    >
      <Flex
        direction={{ base: 'column', md: 'row' }}
        h="full"
        align="center"
        justify="center"
      >
        <Flex
          direction={{ base: 'column', md: 'row' }}
          justify="center"
          w="full"
          gap={{ base: '16px', md: '24px' }}
          align="center"
          wrap={{ base: 'nowrap', md: 'wrap', lg: 'nowrap' }}
        >
          <Flex
            alignItems="center"
            gap={{ base: '2px', md: '4px' }}
            justifyContent="center"
            direction={{ base: 'row', sm: 'row' }}
            minW="fit-content"
          >
            <Text
              color="black"
              fontSize={{ base: 'xs', md: 'sm' }}
              fontWeight="medium"
              whiteSpace="nowrap"
            >
              Total de produtos:
            </Text>
            <Text
              fontWeight="semibold"
              color="black"
              fontSize={{ base: 'md', md: 'xl' }}
              lineHeight="none"
              ml={{ base: '4px', md: '0' }}
            >
              {DecimalMask(
                totalProdutos,
                casasDecimais.casasDecimaisQuantidade,
                casasDecimais.casasDecimaisQuantidade
              )}
            </Text>
          </Flex>

          <Divider
            color="gray.700"
            orientation={!isResponsiveVersion ? 'vertical' : 'horizontal'}
            h={{ base: '1px', md: '20px' }}
            w={{ base: 'full', md: '1px' }}
            display={{ base: 'block', md: 'block' }}
          />

          <Flex
            alignItems="center"
            justifyContent="center"
            gap={{ base: '2px', md: '4px' }}
            direction={{ base: 'row', sm: 'row' }}
            minW="fit-content"
          >
            <Text
              color="black"
              fontSize={{ base: 'xs', md: 'sm' }}
              fontWeight="medium"
              whiteSpace="nowrap"
            >
              Quantidade de itens:
            </Text>
            <Text
              fontWeight="semibold"
              color="black"
              fontSize={{ base: 'md', md: 'xl' }}
              lineHeight="none"
              ml={{ base: '4px', md: '0' }}
            >
              {DecimalMask(
                quantidadeItens,
                casasDecimais.casasDecimaisQuantidade,
                casasDecimais.casasDecimaisQuantidade
              )}
            </Text>
          </Flex>

          <Divider
            color="gray.700"
            orientation={!isResponsiveVersion ? 'vertical' : 'horizontal'}
            h={{ base: '1px', md: '20px' }}
            w={{ base: 'full', md: '1px' }}
            display={{ base: 'block', md: 'block' }}
          />

          <Flex
            gap={{ base: '2px', md: '4px' }}
            alignItems="center"
            justifyContent="center"
            direction={{ base: 'row', sm: 'row' }}
            minW="fit-content"
          >
            <Text
              color="black"
              fontSize={{ base: 'xs', md: 'sm' }}
              fontWeight="medium"
              whiteSpace="nowrap"
            >
              Total da entrada:
            </Text>
            <Box ml={{ base: '4px', md: '0' }}>
              <TextValor
                casasDecimais={2}
                valor={valorTotalProdutos}
                color="black"
                fontSize={{ base: 'md', md: 'xl' }}
                fontWeight="semibold"
                symbolFontSize={{ base: '2xs', md: 'xs' }}
                symbolFontWeight="semibold"
              />
            </Box>
          </Flex>
        </Flex>
      </Flex>
    </Box>
  );
};

export default TotalizadoresFixos;
